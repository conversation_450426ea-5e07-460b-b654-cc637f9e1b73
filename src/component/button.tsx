import type { ButtonHTMLAttributes } from "react";
import React from "react";

type ButtonProps = {
  ref?: React.RefObject<HTMLButtonElement>;
  children: React.ReactNode;
  className?: string;
  defaultWhiteText?: boolean;
} & ButtonHTMLAttributes<HTMLButtonElement>;

const Button: React.FC<ButtonProps> = ({
  children,
  className = "",
  defaultWhiteText = true,
  ref,
  ...props
}) => {
  return (
    <button
      ref={ref}
      className={`${
        defaultWhiteText ? " text-white " : ""
      } overflow-hidden whitespace-nowrap rounded-lg bg-primary px-4 py-2 font-bold  shadow-md transition-colors duration-300 hover:brightness-90 disabled:cursor-not-allowed disabled:brightness-90 ${className} `}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
