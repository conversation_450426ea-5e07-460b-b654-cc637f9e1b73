"use client";
import React, { useState } from "react";
import NotificationDropdownItem from "./NotificationDropdownItem";
import Button from "~/component/button";
import { FaBell } from "react-icons/fa";

interface Notification {
  title: string;
  description: string;
  time: string;
  iconUrl?: string;
  imageUrl?: string;
}

interface NotificationDropdownProps {
  notifications: Notification[];
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ notifications }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <li className="relativ flex cursor-pointer items-center">
      {/* Trigger */}
      <FaBell
        size={20}
        className="ease-nav-brand block bg-transparent p-0 text-primary dark:text-white"
        onClick={() => setIsOpen(!isOpen)}
      />

      <ul
        className={`pointer-events-none absolute left-auto right-0 top-full z-10 mt-2 w-full list-none rounded-xl bg-white shadow-2xl shadow-soft-3xl before:absolute before:-top-2 before:right-5 before:z-20 before:border-b-8 before:border-l-8 before:border-r-8 before:border-t-0 before:border-solid before:border-transparent before:border-b-white before:content-[''] sm:w-100 ${
          isOpen ? "pointer-events-auto opacity-100" : "pointer-events-none opacity-0"
        }`}
      >
        {notifications.map((notification, index) => (
          <NotificationDropdownItem key={index} {...notification} />
        ))}
      </ul>
    </li>
  );
};

export default NotificationDropdown;
