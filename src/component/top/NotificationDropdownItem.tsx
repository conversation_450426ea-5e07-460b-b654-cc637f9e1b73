"use client";
import React from "react";
import { FaMessage } from "react-icons/fa6";
import { FaClock } from "react-icons/fa";

interface NotificationDropdownItemProps {
  title: string;
  description: string;
  time: string;
  iconUrl?: string;
  imageUrl?: string;
}

const NotificationDropdownItem: React.FC<NotificationDropdownItemProps> = ({
  title,
  description,
  time,
  iconUrl,
  imageUrl,
}) => {
  return (
    <li className="relative rounded-2xl  bg-white  ">
      <div className="block w-full whitespace-nowrap  p-2 duration-300 ease-soft lg:transition-colors">
        <div className="flex rounded-lg px-2 py-1 hover:bg-gray-200 hover:text-slate-700">
          {/* Icon */}

          {/* Text Content */}
          <div className="flex flex-col justify-center">
            <h6 className="mb-1 text-sm font-normal leading-normal">
              <span className="font-semibold">{title}</span>
            </h6>
            <p className="mb-0 text-xs leading-tight text-slate-400 dark:opacity-80">
              <FaClock className="mr-1"></FaClock>

              {time}
            </p>
          </div>
        </div>
      </div>
    </li>
  );
};

export default NotificationDropdownItem;
