import { getAllData } from "~/utils/longship";
import RedisClient from "../../utils/redis/redisClient";
import prisma from "../db/prisma";
import { endpoints } from "../api/longship/constants";

const getCharger = async () => {
  const chargepoints = await getAllData(endpoints.CHARGEPOINTS);
  const chargepoints_with_evses = chargepoints.filter(
    (chargepoint) => chargepoint?.evses?.length > 0,
  );
  for (const charger of chargepoints_with_evses) {
    if (charger.updated == "0001-01-01T00:00:00") {
      charger.updated = null;
    }
    delete charger.tokenGroups;
    delete charger.evses;
    if (charger.useTenantFee == null) {
      charger.useTenantFee = false;
    }
    if (charger.ou === null) {
      charger.ou = undefined;
    }
    if (charger.dateCreated == "0001-01-01T00:00:00") {
      charger.dateCreated = "2000-01-01T00:00:00.000000Z";
    }
    try {
      await prisma.chargePoint.upsert({
        where: {
          chargePointId: charger.chargePointId,
        },
        update: {
          ...charger,
        },
        create: {
          ...charger,
        },
      });
    } catch (e) {
      console.log(e);
    }
  }

  const redisClient = RedisClient.getInstance();
  return await redisClient.set("charger", chargepoints_with_evses);
};

export default getCharger;
