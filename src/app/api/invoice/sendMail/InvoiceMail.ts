import { KindOfInvoice } from "@prisma/client";
import type { InvoiceWithFilesContactUser } from "./route";
import { convertUtcToCet } from "~/utils/date/date";

interface GetMessageProps {
  invoice: InvoiceWithFilesContactUser;
}

export const defaultMailFooter =
  `\nEulektro GmbH\\n\\n` +
  `Werderstraße 69\n` +
  `28199 Bremen\n` +
  `+49 421 17 51 28 90\n` +
  `<EMAIL>` +
  `\n\n`;

export const getMitarbeiterClubInvoiceMailMessage = ({
  invoice,
}: GetMessageProps): [string, string] => {
  const subjectDe = `Neue Rechnung für Ihre Ladevorgänge ${invoice.invoiceNumber}`;

  const invoiceMailDe =
    `Sehr geehrte Damen und Herren,\n` +
    `\n` +
    `Im Anhang dieser E-Mail finden Sie die Rechnung für Ihre Ladevorgänge im Zeitraum von ${
      invoice.servicePeriodFrom && convertUtcToCet(invoice.servicePeriodFrom)
    } bis ${invoice.servicePeriodTo && convertUtcToCet(invoice.servicePeriodTo)}.\n` +
    `\n` +
    `Rechnungsdetails:\n` +
    `Rechnungsnummer: ${invoice.invoiceNumber}\n` +
    `Rechnungsdatum: ${invoice.invoiceDate?.toLocaleDateString("de-DE", {
      timeZone: "Europe/Berlin",
    })}\n` +
    `\n` +
    `Die Rechnung wird in den nächsten Tagen über Ihr Standard Zahlungsmittel beglichen` +
    `\n` +
    `Für eventuelle Rückfragen zur Rechnung stehen wir Ihnen gerne zur Verfügung. Sie können uns per E-Mail über <EMAIL> oder telefonisch unter +49 421 ******** erreichen.\n` +
    `\n` +
    `Mit freundlichen Grüßen,\n` +
    `\n` +
    `Team Eulektro\n` +
    `Eulektro GmbH\n\n` +
    `Werderstraße 69\n` +
    `28199 Bremen\n` +
    `+49 421 17 51 28 90\n` +
    `<EMAIL>` +
    `\n\n\n\n`;

  return [subjectDe, invoiceMailDe];
};

export const getRoamingInvoiceMailMessage = ({ invoice }: GetMessageProps): [string, string] => {
  const bankName = `Olinda Zweigniederlassung Deutschland, Potsdamer Platz 1, 10785 Berlin`;
  const bankAccountOwner = `Eulektro GmbH`;
  const bankAccountIBAN = `**********************`;
  const bankAccountBIC = `QNTODEB2XXX`;
  const bankRef = `Invoice ${invoice.invoiceNumber}`;

  const paymentDate = new Date(Date.now() + 12096e5); // 14 days from now

  const transactionTextDe =
    `Bitte begleichen Sie den ausstehenden Betrag bis zum ${paymentDate.toLocaleDateString(
      "de-DE",
    )} auf folgendes Konto:\n` +
    `\n` +
    `Bank: ${bankName}\n` +
    `Kontoinhaber: ${bankAccountOwner}\n` +
    `IBAN: ${bankAccountIBAN}\n` +
    `BIC: ${bankAccountBIC}\n` +
    `Verwendungszweck: ${bankRef}\n`;

  const sepaTextDe =
    "Der ausstehende Betrag wird von dem bei uns hinterlegtem Konto per Lastschriftmandat eingezogen ";

  const subjectDe = `CPO Invoice (DE*EUL) ${invoice.invoiceNumber}`;
  const invoiceMailDe =
    `Sehr geehrte Damen und Herren,\n` +
    `\n` +
    `Im Anhang dieser E-Mail finden Sie die Rechnung für die Nutzung unserer Ladeinfrastruktur im Zeitraum von ${
      invoice.servicePeriodFrom && convertUtcToCet(invoice.servicePeriodFrom)
    } bis ${invoice.servicePeriodTo && convertUtcToCet(invoice.servicePeriodTo)}.\n` +
    `\n` +
    `Rechnungsdetails:\n` +
    `Rechnungsnummer: ${invoice.invoiceNumber}\n` +
    `Rechnungsdatum: ${invoice.invoiceDate?.toLocaleDateString("de-DE", {
      timeZone: "Europe/Berlin",
    })}\n` +
    `\n` +
    `${!invoice?.contact?.stripeCustomerId ? transactionTextDe : sepaTextDe}` +
    `\n` +
    `Für eventuelle Rückfragen zur Rechnung stehen wir Ihnen gerne zur Verfügung. Sie können uns per E-<NAME_EMAIL> oder telefonisch unter +49 421 ******** erreichen.\n` +
    `\n` +
    `Vielen Dank für Ihr Vertrauen in unsere Ladeinfrastruktur. Wir freuen uns auf die weitere Zusammenarbeit und wünschen Ihnen einen erfolgreichen Monat.\n` +
    `\n` +
    `Mit freundlichen Grüßen,\n` +
    `\n` +
    `Team Eulektro (DE*EUL)\n` +
    `Eulektro GmbH\n\n` +
    `Werderstraße 69\n` +
    `28199 Bremen\n` +
    `+49 421 17 51 28 90\n` +
    `<EMAIL>` +
    `\n\n\n\n`;

  const subjectEn = `CPO Invoice (DE*EUL) ${invoice.invoiceNumber}`;
  const invoiceMailEn =
    `Hello ${invoice?.contact?.companyName},\n` +
    `\n` +
    `Please find attached the invoice for the use of our charging infrastructure for the period from ${
      invoice.servicePeriodFrom && convertUtcToCet(invoice.servicePeriodFrom, "en-EN")
    } to ${invoice.servicePeriodTo && convertUtcToCet(invoice.servicePeriodTo, "en-EN")}.\n` +
    `\n` +
    `Invoice Details:\n` +
    `Invoice Number: ${invoice.invoiceNumber}\n` +
    `Invoice Date: ${invoice.invoiceDate?.toLocaleDateString("en-EN", {
      timeZone: "Europe/Berlin",
    })}\n` +
    `\n` +
    `Please settle the outstanding amount by the ${paymentDate.toLocaleDateString(
      "en-EN",
    )} to the following account:\n` +
    `\n` +
    `Bank: ${bankName}\n` +
    `Account Holder: ${bankAccountOwner}\n` +
    `IBAN: ${bankAccountIBAN}\n` +
    `BIC: ${bankAccountBIC}\n` +
    `Payment Reference: ${bankRef}\n` +
    `\n` +
    `If you have any questions regarding the invoice, please do not hesitate to contact us. You can reach us via <NAME_EMAIL> or by phone at +49 421 ********.\n` +
    `\n` +
    `Thank you for your trust in our charging infrastructure. We look forward to our continued collaboration and wish you a successful month.\n` +
    `\n` +
    `Best regards,` +
    `Team Eulektro (DE*EUL)\n` +
    `Eulektro GmbH\n\n` +
    `Werderstraße 69\n` +
    `28199 Bremen\n` +
    `+49 421 17 51 28 90\n` +
    `<EMAIL>` +
    `\n\n\n\n`;

  const cancelMailSubjecteDe = `Stornorechnung ${invoice.invoiceNumber}`;
  const cancelMailDe =
    `Sehr geehrte Damen und Herren,\n` +
    `\n` +
    `Im Anhang dieser E-Mail finden Sie die Stornorechnung ${invoice.invoiceNumber}.\n` +
    `\n` +
    `Für eventuelle Rückfragen zur Rechnung stehen wir Ihnen gerne zur Verfügung.\n` +
    `Sie können uns per E-<NAME_EMAIL> oder telefonisch unter +49 421 ******** erreichen.\n\n` +
    `Mit freundlichen Grüßen,` +
    `Team Eulektro (DE*EUL)\n` +
    `Eulektro GmbH\n\n` +
    `Werderstraße 69\n` +
    `28199 Bremen\n` +
    `+49 421 17 51 28 90\n` +
    `<EMAIL>` +
    `\n\n\n\n`;

  const cancelMailSubjectEn = `cancellation invoice ${invoice.invoiceNumber}`;
  const cancelMailEn =
    `Hello ${invoice?.contact?.companyName},\n` +
    `In the attachment of this email, you will find the cancellation invoice ${invoice.invoiceNumber}\n` +
    `\n\n` +
    `Best regards,` +
    `Team Eulektro (DE*EUL)\n` +
    `Eulektro GmbH\n\n` +
    `Werderstraße 69\n` +
    `28199 Bremen\n` +
    `+49 421 17 51 28 90\n` +
    `<EMAIL>` +
    `\n\n\n\n`;

  const creditSubjectDe = `Gutschrift ${invoice.invoiceNumber}`;
  const creditMailDe =
    `Sehr geehrte Damen und Herren,\n` +
    `\n` +
    `Im Anhang dieser E-Mail finden Sie die Gutschrift für die Gutschrift-Nr.:${invoice.invoiceNumber}.\n` +
    `\n` +
    `Bitte beachten Sie die Details und zögern Sie nicht, uns bei Fragen zu kontaktieren.\n` +
    `Sie erreichen uns per E-<NAME_EMAIL> oder telefonisch unter +49 421 ********.\n\n` +
    `Mit freundlichen Grüßen,\n` +
    `Team Eulektro\n` +
    `Eulektro GmbH\n\n` +
    `Werderstraße 69\n` +
    `28199 Bremen\n` +
    `+49 421 17 51 28 90\n` +
    `<EMAIL>` +
    `\n\n\n\n`;

  const creditSubjectEn = `Credit ${invoice.invoiceNumber}`;
  const creditMailEn =
    `Hello ${invoice?.contact?.companyName},\n` +
    `\n` +
    `Please find attached the credit note for credit number ${invoice.invoiceNumber}.\n` +
    `\n` +
    `Please review the details and do not hesitate to contact us with any questions. You can reach us via <NAME_EMAIL> or by phone at +49 421 ********.\n` +
    `\n` +
    `Best regards,\n` +
    `Team Eulektro\n` +
    `Eulektro GmbH\n\n` +
    `Werderstraße 69\n` +
    `28199 Bremen\n` +
    `+49 421 17 51 28 90\n` +
    `<EMAIL>` +
    `\n\n\n\n`;

  switch (invoice?.contact?.invoiceLanguageCode) {
    case "DE":
      switch (invoice.kindOfInvoice) {
        case KindOfInvoice.INVOICE:
          return [subjectDe, invoiceMailDe];
        case KindOfInvoice.STORNO:
          return [cancelMailSubjecteDe, cancelMailDe];
        case KindOfInvoice.CREDIT:
          return [creditSubjectDe, creditMailDe];
      }
      break;
    case "EN":
      switch (invoice.kindOfInvoice) {
        case KindOfInvoice.INVOICE:
          return [subjectEn, invoiceMailEn];
        case KindOfInvoice.STORNO:
          return [cancelMailSubjectEn, cancelMailEn];
        case KindOfInvoice.CREDIT:
          return [creditSubjectEn, creditMailEn];
      }
      break;
  }

  return ["", ""];
};

export const getCdrMailMessage = ({ invoice }: GetMessageProps): [string, string] => {
  const subjectDe = `CDRs for invoice ${invoice.invoiceNumber}`;
  const invoiceMailDe =
    "Sehr geehrte Damen und Herren,\n" +
    "\n" +
    `anbei finden Sie die Charge Detail Records (CDRs) für die ${
      invoice.kindOfInvoice == KindOfInvoice.INVOICE ? "Rechnung" : "Gutschrift"
    } ${invoice.invoiceNumber}.` +
    "\n" +
    `Die ${
      invoice.kindOfInvoice == KindOfInvoice.INVOICE ? "Rechnung" : "Gutschrift"
    } wurde in einer separaten E-Mail an ${invoice?.contact?.invoiceMail} gesendet.` +
    "\n" +
    "Sollten Sie Fragen zu den Datensätzen haben, zögern Sie nicht uns zu kontaktieren.\n" +
    "\n" +
    "\n" +
    "Mit freundlichen Grüßen,\n" +
    "Team Eulektro (DE*EUL)\n\n" +
    "Werderstraße 69\n" +
    "28199 Bremen\n" +
    "<EMAIL>" +
    "\n\n\n\n";

  const subjectEn = `CDRs for invoice ${invoice.invoiceNumber}`;
  const invoiceMailEn =
    "Hello,\n" +
    "\n" +
    `Please find attached the Charge Detail Records (CDRs) for the ${
      invoice.kindOfInvoice == KindOfInvoice.INVOICE ? "invoice" : "credit"
    }  ${invoice.invoiceNumber}.` +
    "\n" +
    `The ${
      invoice.kindOfInvoice == KindOfInvoice.INVOICE ? "invoice" : "credit"
    } has been sent in a separate email to ${invoice?.contact?.invoiceMail}.` +
    "\n" +
    "If you have any questions regarding the data records, please do not hesitate to contact us.\n" +
    "\n" +
    "\n" +
    "Kind regards,\n" +
    "Team Eulektro (DE*EUL)\n\n" +
    "Werderstraße 69\n" +
    "28199 Bremen\n" +
    "<EMAIL>" +
    "\n\n\n\n";

  switch (invoice?.contact?.invoiceLanguageCode) {
    case "DE":
      return [subjectDe, invoiceMailDe];
    case "EN":
      return [subjectEn, invoiceMailEn];
  }
  return ["Mail from Eulektro", "Content generation not successfull, please ignore email"];
};
