"use client";

import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import type {
  FirstDataRenderedEvent,
  GetRowIdFunc,
  GetRowIdParams,
  GridApi,
  GridOptions,
  GridReadyEvent,
  ICellRendererParams,
} from "ag-grid-community";
import type {
  ColDef,
  ColGroupDef,
  ValueFormatterParams,
  ValueGetterParams,
} from "ag-grid-community/dist/lib/entities/colDef";
import { LicenseManager } from "ag-grid-enterprise";
import { ImPower, ImUnlocked } from "react-icons/im";

import {
  FaArrowDown,
  FaArrowRight,
  FaChargingStation,
  FaCheckCircle,
  FaLockOpen,
  FaPlay,
  FaRegQuestionCircle,
  FaStop,
} from "react-icons/fa";

import { MdAddTask, Md<PERSON><PERSON>r, MdOutlineEuroSymbol, MdWarning } from "react-icons/md";

import { TbPlugConnected } from "react-icons/tb";
import Table from "../../../utils/table/table";

import { AiOutlineStock } from "react-icons/ai";
import { userStore } from "~/server/zustand/store";
import RealtimeWidget from "~/app/(app)/component/RealtimeWidget";
import { LuEuro, LuParkingCircleOff } from "react-icons/lu";
import { ConnectorStatus } from "~/app/api/realtime/route";
import { useSession } from "next-auth/react";
import { MonitoringEvent, OCPPErrorStatusMessage, Role } from "@prisma/client";
import StockPriceChart from "~/app/(app)/component/StockPriceChart";
import MessageDialog from "~/app/(app)/util/MessageDialog";
import { FaTriangleExclamation } from "react-icons/fa6";
import ConnectorStateBar from "~/app/(app)/component/ConnectorStateBar";

LicenseManager.setLicenseKey(
  "CompanyName=Zeitgleich GmbH,LicensedApplication=BEAST,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-016007,ExpiryDate=2_July_2022_[v2]_MTY1NjcxNjQwMDAwMA==579f3e57c0b0b4db77e0428d0cac15be",
);

/*
interface RealtimeData {
  current_kw_charging: number;
  connector_status: Record<string, unknown>;
  yesterday_valid_kWh: number;
  today_cdr: number;
  today_valid_kWh: number;
  realtime_chargepoint_data: any[];
  charging_session: number;
  status: string;
  number_of_charging: number;
  active_by_station: string;
  today_current_kWh_charging: string;
  yesterday_current_kWh_charging: string;
  current_kw_by_evse: string;
  connector_status_by_id: string;
  stockPrice: number[] | null;
  today_msp: MspProps[];
  chargePointErrors: ChargePointError[];
  emp: string;
}*/

const RealtimeTable = () => {
  const [realtimeData, setRealtimeData] = useState<any>({
    current_kw_charging: 0,
    connector_status: {},
    yesterday_valid_kWh: null,
    today_cdr: 0,
    today_valid_kWh: null,
    realtime_chargepoint_data: [],
    charging_session: 0,
    status: "",
    number_of_charging: 0,
    active_by_station: "",
    today_current_kWh_charging: "",
    yesterday_current_kWh_charging: "",
    current_kw_by_evse: "",
    connector_status_by_id: "",
    stockPrice: null,
    chargePointErrors: [],
    emp: "",
    parkingSensor: null,
    ocppMessages: [],
  });

  const state = userStore();
  const { data: session } = useSession();
  const [gridApi, setGridApi] = useState<GridApi | undefined>();
  const [commandSent, setCommandSent] = useState<boolean>(false);
  const [commandError, setCommandError] = useState<boolean>(false);
  const [kWDelta, setKwDelta] = useState(0);

  const [oldKW, setOldKW] = useState({
    timestamp: new Date().getTime(),
    kw: 0,
  });
  const [chargerErrorsOpen] = useState<boolean>(true);

  const [revenue, setRevenue] = useState({
    todayRevenue: undefined,
    yesterdayRevenue: undefined,
    todayGrossMargin: undefined,
    yesterdayGrossMargin: undefined,
  });

  const getData = useCallback(async () => {
    const realtimeRawData = await fetch("/api/realtime", {
      next: { revalidate: 0 },
      method: "POST",
      body: JSON.stringify({ ouId: state.selectedOuId }),
    });
    const realtimeData = await realtimeRawData.json();
    setRealtimeData(realtimeData);

    const invoice_realtime_response = await fetch("/api/invoice/realtime", {
      next: { revalidate: 0 },
      method: "POST",
      body: JSON.stringify({ ouId: state.selectedOuId }),
    });
    const invoice_realtime = await invoice_realtime_response.json();
    setRevenue(invoice_realtime);

    return realtimeData.realtime_chargepoint_data;
  }, [state.selectedOuId]);

  useEffect(() => {
    const nowTimestamp = new Date().getTime();
    if (oldKW.timestamp < nowTimestamp - 5 * 60 * 1000) {
      setKwDelta(realtimeData.current_kw_charging - oldKW.kw);
      setOldKW({
        timestamp: nowTimestamp,
        kw: realtimeData.current_kw_charging,
      });
    }
    if (oldKW.kw == 0) {
      setOldKW({
        timestamp: nowTimestamp,
        kw: realtimeData.current_kw_charging,
      });
    }
  }, [realtimeData]);

  const fetchData = useCallback(
    async (gridApi: GridApi, firstData = false) => {
      const data = await getData();
      if (firstData) {
        if (data) {
          gridApi?.setRowData(data);
        }
      } else {
        gridApi?.applyTransactionAsync({ update: data });
      }
    },
    [gridApi, state.selectedOuId],
  );

  const refreshActionCells = () => {
    if (session?.user) {
      gridApi?.refreshCells({ force: true, columns: ["id"] });
    }
  };

  useEffect(() => {
    if (!gridApi) {
      return;
    }
    fetchData(gridApi, true).catch(console.error);
    const intervalId = setInterval(() => {
      fetchData(gridApi).catch(console.error);
    }, 10000);
    return () => clearInterval(intervalId);
  }, [gridApi, state.selectedOuId]);

  const timeSinceRenderer = (params: ValueFormatterParams) => {
    if (params?.node?.group) {
      //when grouped show no time
      return "";
    }
    const dateField = params.value;
    const currentDate = Date.now();
    const dateValue = new Date(dateField).getTime();
    const diff = currentDate - dateValue;
    const diffInMinutes = diff / 1000 / 60;
    const hours = Math.floor(diffInMinutes / 60)
      .toString()
      .padStart(2, "0");
    const minutes = Math.floor(diffInMinutes % 60)
      .toString()
      .padStart(2, "0");
    return `${hours}:${minutes}`;
  };

  const stateCellRenderer = (params: ValueFormatterParams) => {
    let icon = "";
    switch (params.value) {
      case "Charging":
        icon = "⚡️";
        break;
      case "Available":
        icon = "🅿️";
        break;
      case "Preparing":
        icon = "🔌";
        break;
      case "SuspendedEV":
      case "SuspendedEVSE":
        icon = "⏸️";
        break;
      case "Finishing":
        icon = "🏁";
        break;
      case "Unavailable":
        icon = "❌";
        break;
    }
    return `${icon} ${params?.value ?? ""}`;
  };

  const formatNumber = (number: number, toFixed = 2) => {
    // this puts commas into the number e.g. 1000 goes to 1,000,
    // i pulled this from stack overflow, i have no idea how it works
    return number ? number.toFixed(toFixed) : "";
  };

  const getParksensorIcon = (sensorState: number | null | undefined, index: number) => {
    if (sensorState === 1) {
      return <LuParkingCircleOff key={index} size={20} color={"red"} />;
    }
    if (sensorState === 2) {
      return <LuParkingCircleOff key={index} size={20} color={"green"} />;
    }
    if (sensorState == 0) {
      return <FaRegQuestionCircle key={index} size={20} color={"gray"} />;
    }

    return <LuParkingCircleOff key={index} size={20} color={"gray"} />;
  };

  const [columnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    { field: "id", headerName: "Id", hide: true },
    { field: "displayName", headerName: "EVSE-ID", minWidth: 160 },

    {
      field: "currentKw",
      headerName: "kW",
      aggFunc: "sum",
      sort: "desc",
      minWidth: 100,
      enableValue: true,
      valueFormatter: (params) => formatNumber(params.value),
      cellRenderer: "agAnimateShowChangeCellRenderer",
    },
    {
      field: "kwhInCurrentSession",
      headerName: "kWh (Jetzt)",
      aggFunc: "sum",
      enableValue: true,
      minWidth: 100,
      valueFormatter: (params) => formatNumber(params.value),
      cellRenderer: "agAnimateShowChangeCellRenderer",
    },
    {
      field: "kWhToday",
      headerName: "kWh (Heute)",
      aggFunc: "sum",
      enableValue: true,
      minWidth: 100,
      valueFormatter: (params) => formatNumber(params.value),
      cellRenderer: "agAnimateShowChangeCellRenderer",
    },
    {
      field: "operationalStatus",
      headerName: "Zustand",
      enableRowGroup: true,
      minWidth: 80,
      cellRenderer: stateCellRenderer,
      aggFunc: (params) => {
        return `${params.values.filter((x) => x == "Charging").length} Charging`;
      },
    },
    { field: "emp", headerName: session?.user.role == Role.ADMIN ? "EMP" : "Freigeschaltet durch" },

    {
      field: "place",
      headerName: "Ort",
      enableRowGroup: true,
      rowGroup: true,
      hide: true,
      aggFunc: "count",
    },

    {
      field: "lastStateChange",
      headerName: "Seit",
      cellRenderer: timeSinceRenderer,
      aggFunc: "first",
    },
    {
      field: "id",
      headerName: "Action",
      cellRenderer: (params: ICellRendererParams) => {
        if (params?.node?.group) {
          return "Commands";
        }
        return (
          <div className={"flex h-full  flex-row items-center gap-3"}>
            <FaPlay
              onClick={() => remoteStart(params?.data?.id)}
              size={20}
              className={"text-green-300 hover:cursor-pointer"}
            />
            <FaStop
              size={20}
              onClick={() => remoteStop(params?.data?.id)}
              className={"text-red-300 hover:cursor-pointer"}
            />
            <FaLockOpen
              onClick={() => unclockConnector(params?.data?.id)}
              size={20}
              className={"hover:cursor-pointer"}
            />
          </div>
        );
      },
    },
  ]);

  const gridOptions: GridOptions = {
    rowModelType: "clientSide",
    autoGroupColumnDef: {
      headerName: "Standort",
      minWidth: 150,
      aggFunc: "count",
      cellRendererParams: {
        footerValueGetter: (params: ValueGetterParams) => {
          return `Standorte: ${params?.node?.childrenAfterFilter?.length}`;
        },
      },
    },

    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    suppressAggFuncInHeader: true,
    groupIncludeFooter: true,
    // includes grand total
    groupIncludeTotalFooter: true,
    rowHeight: 30,
    sideBar: {
      toolPanels: [
        {
          id: "columns",
          labelDefault: "Columns",
          labelKey: "columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          minWidth: 225,
          maxWidth: 225,
          width: 225,
        },
        {
          id: "filters",
          labelDefault: "Filters",
          labelKey: "filters",
          iconKey: "filter",
          toolPanel: "agFiltersToolPanel",
          minWidth: 180,
          maxWidth: 400,
          width: 250,
        },
      ],
      position: "right",
      defaultToolPanel: "",
    },
  };
  const remoteStart = async (evseId: string | undefined) => {
    if (!evseId) {
      console.warn("no valid evseID");
    }
    if (!confirm(`Ladepunkt:${evseId} wirklich starten?`)) {
      return;
    }
    const response = await fetch("/api/longship/command/remoteStart", {
      method: "POST",
      body: JSON.stringify({ evseId: evseId }),
    });
    if (response.ok) {
      showCommandSent();
    } else {
      showCommandError();
    }
  };

  const remoteStop = async (evseId: string | undefined) => {
    if (!evseId) {
      console.warn("no valid evseID");
    }
    if (!confirm(`Ladepunkt:${evseId} wirklich stoppen?`)) {
      return;
    }
    const response = await fetch("/api/longship/command/remoteStop", {
      method: "POST",
      body: JSON.stringify({ evseId: evseId }),
    });
    if (response.ok) {
      showCommandSent();
    } else {
      showCommandError();
    }
  };

  const showCommandSent = () => {
    setCommandSent(true);
    setTimeout(() => {
      setCommandSent(false);
    }, 2000); // Spätestens nach 2s ausgeblendet (auch wenn opacity duration länger ist)
  };

  const showCommandError = () => {
    setCommandError(true);
    setTimeout(() => {
      setCommandError(false);
    }, 2000); // Spätestens nach 2s ausgeblendet (auch wenn opacity duration länger ist)
  };

  const unclockConnector = async (evseId: string | undefined) => {
    if (!evseId) {
      console.warn("no valid evseID");
    }
    if (!confirm(`Ladepunkt:${evseId} wirklich entriegeln?`)) {
      return;
    }
    const response = await fetch("/api/longship/command/unlockConnector", {
      method: "POST",
      body: JSON.stringify({ evseId: evseId }),
    });
    if (response.ok) {
      showCommandSent();
    } else {
      showCommandError();
    }
  };

  const handleGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
    params.api.addEventListener("firstDataRendered", function (event: FirstDataRenderedEvent) {
      event.columnApi.autoSizeAllColumns(true);
    });
  };

  const wattToKw = (x: number, rounded: number) => {
    return parseFloat((x / 1000).toFixed(rounded));
  };

  const getRowId = useMemo<GetRowIdFunc>(() => {
    return (params: GetRowIdParams) => params.data.id;
  }, []);

  const empYesterdayList = useMemo(() => {
    if (realtimeData.yesterday_msp) {
      return Object.entries(realtimeData.yesterday_msp)
        .sort(([, a], [, b]) => b.sessions - a.sessions)
        .map(([key, value]) => `${key}: ${value.sessions} / ${value.kwh.toFixed(0)}`);
    } else {
      return []; // Return an empty array if there's no data
    }
  }, [realtimeData.yesterday_msp]);
  // Workload Widget List
  const [workloadList, numOnline] = useMemo(() => {
    const workloadList = realtimeData.connector_status
      ? Object.entries(realtimeData.connector_status)
          .sort(([, a], [, b]) => b - a)
          .map(([key, value]) => `${key}: ${value}`)
      : [];
    const numOnline = realtimeData?.connector_status
      ? Object.values(realtimeData.connector_status)?.reduce((a, b) => a + b, 0)
      : 0;

    return [workloadList, numOnline];
  }, [realtimeData.connector_status]);

  // Chargepoint Errors Widget List
  const chargepointErrorsList = useMemo(() => {
    return chargerErrorsOpen && realtimeData.chargePointErrors
      ? realtimeData.chargePointErrors.map(
          (errorItem) => `${errorItem.chargePointId}: ${errorItem.error}`,
        )
      : [];
  }, [chargerErrorsOpen, realtimeData.chargePointErrors]);

  // EMP Today Widget List
  const empTodayList = useMemo(() => {
    return realtimeData.today_msp
      ? Object.entries(realtimeData.today_msp)
          .sort(([, a], [, b]) => b.sessions - a.sessions)
          .map(([key, value]) => `${key}: ${value.sessions} / ${value.kwh.toFixed(0)}`)
      : [];
  }, [realtimeData.today_msp]);

  return (
    <div className={"flex w-full flex-col"}>
      <RealtimeWidget caption={"Connector State"} loading={!realtimeData.connector_status}>
        <ConnectorStateBar data={realtimeData.connector_status} />
      </RealtimeWidget>
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4 ">
        <>
          <RealtimeWidget
            caption="Current kW"
            loading={!realtimeData.current_kw_charging}
            icon={<ImPower size={21} />}
          >
            <h5 className="mb-0 font-bold dark:text-white">
              {realtimeData.current_kw_charging
                ? `${wattToKw(realtimeData.current_kw_charging, 0)} kW`
                : "Loading..."}{" "}
              {kWDelta != 0 ? (
                kWDelta >= 0 ? (
                  <span className="font-weight-bolder text-sm leading-normal text-lime-500">
                    + {wattToKw(kWDelta, 2)} kW
                  </span>
                ) : (
                  <span className="font-weight-bolder text-sm leading-normal text-red-600">
                    - {Math.abs(wattToKw(kWDelta, 2))} kW
                  </span>
                )
              ) : (
                ""
              )}
              <span className="font-weight-bolder text-sm leading-normal text-lime-500"></span>
            </h5>{" "}
          </RealtimeWidget>

          {/* Energy Today Widget */}
          <RealtimeWidget
            caption="Energy Today"
            loading={realtimeData.today_valid_kWh == null ? true : false}
            primaryTitle="Energy Today"
            primaryValue={`${realtimeData?.today_valid_kWh?.toFixed(0)}`}
            secondaryValue={
              realtimeData.yesterday_valid_kWh
                ? `${realtimeData.yesterday_valid_kWh.toFixed(0)}`
                : undefined
            }
            valueUnit="kWh"
            icon={<FaChargingStation size={21} />}
          />

          {/* Revenue Widget */}
          {(session?.user?.role === Role.ADMIN || session?.user?.role === Role.CPO) && (
            <RealtimeWidget
              caption="Revenue"
              loading={!revenue.todayRevenue}
              primaryTitle="Today's Revenue"
              primaryValue={revenue.todayRevenue}
              secondaryValue={revenue.yesterdayRevenue}
              valueUnit="€"
              icon={<MdOutlineEuroSymbol size={21} />}
            />
          )}

          {/* Gross Margin Widget */}
          {(session?.user?.role === Role.ADMIN || session?.user?.role === Role.CPO) && (
            <RealtimeWidget
              caption="Gross Margin"
              loading={!revenue.todayGrossMargin}
              primaryTitle="Today's Gross Margin"
              primaryValue={revenue.todayGrossMargin}
              secondaryValue={revenue.yesterdayGrossMargin}
              valueUnit="€"
              icon={<MdOutlineEuroSymbol size={21} />}
            />
          )}
          <RealtimeWidget
            icon={<AiOutlineStock size={21} />}
            caption={"EPEX Stock Price (ct/kWh)"}
            loading={realtimeData.stockPrice ? false : true}
            className={"col-span-2 2xl:col-span-1"}
          >
            {realtimeData?.stockPrice && (
              <>
                <div className={"flex flex-row items-baseline"}>
                  <p className="mb-0 text-xs font-bold dark:text-white">
                    {realtimeData.stockPrice[0]?.amount.toFixed(2)}
                  </p>
                  <h5 className="mx-2 mb-0 font-bold text-orange-400 dark:text-white">
                    {realtimeData.stockPrice[1]?.amount.toFixed(2)}
                  </h5>
                  <p className="mb-0 text-xs font-bold dark:text-white">
                    {realtimeData.stockPrice[2]?.amount.toFixed(2)}
                  </p>
                </div>
                {(session?.user?.role === Role.ADMIN || session?.user?.role === Role.CPO) && (
                  <StockPriceChart />
                )}
              </>
            )}
          </RealtimeWidget>

          <RealtimeWidget
            caption="Workload"
            loading={!realtimeData.connector_status}
            //list={workloadList}
            icon={<TbPlugConnected size={21} />}
          >
            <div className={"flex flex-col"}>
              <h5 className="mb-0 font-bold dark:text-white">
                <ul className={" text-sm"}>
                  {workloadList?.map((item, index) => {
                    return (
                      <li
                        key={index}
                        className={`${index === 0 ? "mr-7" : ""} ${
                          (item.startsWith("Faulted") || item.startsWith("Unavailable")) &&
                          !item.endsWith("0")
                            ? " text-red-400"
                            : ""
                        }`}
                      >
                        {item}
                      </li>
                    );
                  })}
                  {workloadList.length === 0 && <li>Keine</li>}
                </ul>
              </h5>
              <span
                className={`${
                  realtimeData?.offlineConnectors?.length === 0 ? "text-green-600" : ""
                }`}
              >{`${
                realtimeData?.offlineConnectors?.length === 0 ? "Alle " : ""
              } ${numOnline} Online `}</span>

              {realtimeData?.offlineConnectors?.length > 0 && (
                <>
                  <span
                    className={"text-red-500"}
                  >{`${realtimeData?.offlineConnectors?.length} Offline`}</span>
                  <ul>
                    {realtimeData?.offlineConnectors?.map(
                      (connector: ConnectorStatus, index: number) => (
                        <li className={"ml-4 list-disc text-red-500"} key={index}>
                          {connector.evseId}{" "}
                          {new Date(connector?.last_update).toLocaleTimeString("de")}
                        </li>
                      ),
                    )}
                  </ul>
                </>
              )}
            </div>
          </RealtimeWidget>
          {/* EMP Today Widget */}
          <RealtimeWidget
            caption={`EMP Today (${realtimeData.today_cdr ?? "?"}/${
              realtimeData.today_invalid_cdr ?? "?"
            })`}
            captionTitle={
              "Alle Ladevorgänge von heute gruppiert nach Roaming Partner. In Klammern die Summen (valid/invalid)"
            }
            loading={!realtimeData.today_msp}
            list={empTodayList}
            icon={<MdAddTask size={21} />}
          />

          {/* EMP Yesterday Widget */}
          <RealtimeWidget
            caption={`EMP Yesterday (${realtimeData.yesterday_cdr ?? "?"}/${
              realtimeData.yesterday_invalid_cdr ?? "?"
            })`}
            loading={!realtimeData.yesterday_msp}
            captionTitle={
              "Alle Ladevorgänge von gestern gruppiert nach Roaming Partner. In Klammern die Summen (valid/invalid)"
            }
            list={empYesterdayList}
            icon={<MdAddTask size={21} />}
          />
          {/* Chargepoint Errors Widget */}
          <RealtimeWidget
            caption="Chargepoint Errors"
            loading={!realtimeData.chargePointErrors}
            list={chargepointErrorsList}
            icon={<MdError size={21} />}
          ></RealtimeWidget>

          {session?.user?.role === Role.ADMIN && (
            <>
              <RealtimeWidget
                caption="Messages Today (Plug Locking)"
                loading={!realtimeData.ocppMessages}
                list={realtimeData?.ocppMessages?.map(
                  (message: OCPPErrorStatusMessage) =>
                    `${message.chargePointId}: ${message.errorCode}`,
                )}
                icon={<MdError size={21} />}
              />
              <RealtimeWidget
                className={"col-span-2 2xl:col-span-1"}
                caption="Monitoring Events"
                loading={!realtimeData.monitoringEvents}
                list={realtimeData?.monitoringEvents?.map(
                  (event: MonitoringEvent, index: number) =>
                    event?.href ? (
                      <a key={index} href={event.href}>{`${event.evseId}: ${event.message}`}</a>
                    ) : (
                      `${event.evseId}: ${event.message}`
                    ),
                )}
                icon={<MdWarning size={21} />}
              />
            </>
          )}
        </>
      </div>

      <div className="relative mt-3" style={{ width: "100%", height: 800 }}>
        <Table
          gridId={"realtime"}
          gridOptions={gridOptions}
          columnDefs={columnDefs}
          getRowId={getRowId}
          groupIncludeFooter={true}
          animateRows={true}
          groupIncludeTotalFooter={true}
          onGridReady={handleGridReady}
          onRowDataUpdated={refreshActionCells}
        />

        {commandSent && (
          <div
            className={`transition-opacity duration-1000 ${
              commandSent ? "opacity-100" : "opacity-0"
            } translate-op absolute left-1/2 top-1/2 flex h-10 -translate-x-1/2 -translate-y-1/2 transform items-center justify-center gap-2 rounded-xl bg-secondary p-4 text-primary shadow-md`}
          >
            <span className={"truncate"}>Command gesendet</span>
            <FaCheckCircle className={"text-green-300"} />
          </div>
        )}
        {commandError && (
          <div
            className={`transition-opacity duration-1000 ${
              commandError ? "opacity-100" : "opacity-0"
            } translate-op absolute left-1/2 top-1/2 flex h-10 -translate-x-1/2 -translate-y-1/2 transform items-center justify-center gap-2 rounded-xl bg-secondary p-4 text-primary shadow-md`}
          >
            <span className={"truncate"}>Command senden fehlgeschlagen</span>
            <FaTriangleExclamation className={"text-red-300"} />
          </div>
        )}
      </div>
    </div>
  );
};

export default RealtimeTable;
